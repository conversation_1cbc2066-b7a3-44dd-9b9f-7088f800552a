"""
Life events for background history generation in One Life Isekai
Events are organized by life stage and have conditional requirements
"""

INFANCY_EVENTS = {
    'abandoned_in_woods': {
        'name': 'Abandoned in the Woods',
        'description': 'Your parents left you in the dark forest. You survived, but the trauma marked you.',
        'type': 'trauma',
        'stat_changes': {'WILL': -10, 'beauty': -5, 'VIT': 5},
        'new_traits': ['orphan'],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 2,
        'tags': ['negative', 'rare', 'trauma']
    },
    'blessed_birth': {
        'name': 'Blessed Birth',
        'description': 'You were born under a lucky star. Divine forces smile upon you.',
        'type': 'blessing',
        'stat_changes': {'beauty': 10, 'estimated_lifespan': 5},
        'new_traits': ['lucky'],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 3,
        'tags': ['positive', 'rare', 'divine']
    },
    'sickly_child': {
        'name': 'Sickly Child',
        'description': 'You were born weak and frail, requiring constant care.',
        'type': 'illness',
        'stat_changes': {'VIT': -5, 'immune_system': -20},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 8,
        'tags': ['negative', 'common', 'health']
    },
    'normal_infancy': {
        'name': 'Peaceful Infancy',
        'description': 'You grew up in a loving home with no major incidents.',
        'type': 'normal',
        'stat_changes': {},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 50,
        'tags': ['neutral', 'common']
    }
}

TODDLERHOOD_EVENTS = {
    'first_words_magic': {
        'name': 'First Words Were Magic',
        'description': 'Your first spoken words caused flowers to bloom around you.',
        'type': 'awakening',
        'stat_changes': {'magic_affinity': 20, 'WILL': 10},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {'magic_affinity': {'min': 30}},
        'weight': 5,
        'tags': ['positive', 'rare', 'magic']
    },
    'fell_from_height': {
        'name': 'Fell from Great Height',
        'description': 'You tumbled down stairs but somehow survived with only bruises.',
        'type': 'accident',
        'stat_changes': {'VIT': 3, 'DEX': -2},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 10,
        'tags': ['negative', 'common', 'accident']
    },
    'animal_companion': {
        'name': 'Befriended Wild Animal',
        'description': 'A wild creature took a liking to you and became your companion.',
        'type': 'friendship',
        'stat_changes': {'perception': 10, 'persuasion': 5},
        'new_traits': ['animal_friend'],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 8,
        'tags': ['positive', 'uncommon', 'nature']
    },
    'normal_toddlerhood': {
        'name': 'Normal Development',
        'description': 'You learned to walk and talk like any other child.',
        'type': 'normal',
        'stat_changes': {'DEX': 2, 'INT': 1},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 40,
        'tags': ['neutral', 'common']
    }
}

EARLY_CHILDHOOD_EVENTS = {
    'discovered_by_scholar': {
        'name': 'Discovered by Traveling Scholar',
        'description': 'A wise scholar recognized your potential and began teaching you.',
        'type': 'mentorship',
        'stat_changes': {'INT': 15, 'iq': 20},
        'new_traits': [],
        'unlocked_skills': [{'name': 'sharp_mind', 'level': 1}],
        'requirements': {'iq': {'min': 110}},
        'weight': 3,
        'tags': ['positive', 'rare', 'education']
    },
    'bitten_by_strange_beast': {
        'name': 'Bitten by Strange Beast',
        'description': 'A mysterious creature bit you, leaving a glowing mark that pulses with power.',
        'type': 'transformation',
        'stat_changes': {'WILL': 10, 'chaos_resist': 5, 'magic_affinity': 15},
        'new_traits': ['arcane_scar'],
        'unlocked_skills': [],
        'requirements': {'immune_system': {'max': 100}, 'magic_affinity': {'min': 50}},
        'weight': 2,
        'tags': ['negative', 'rare', 'mystic']
    },
    'started_crafting': {
        'name': 'Began Crafting',
        'description': 'You showed natural talent for making things with your hands.',
        'type': 'skill_discovery',
        'stat_changes': {'DEX': 5},
        'new_traits': [],
        'unlocked_skills': [{'name': 'blacksmithing', 'level': 1}],
        'requirements': {'DEX': {'min': 20}},
        'weight': 12,
        'tags': ['positive', 'common', 'craft']
    },
    'lost_in_forest': {
        'name': 'Lost in Dark Forest',
        'description': 'You wandered into the woods and spent days alone before being found.',
        'type': 'survival',
        'stat_changes': {'perception': 8, 'VIT': 3, 'beauty': -3},
        'new_traits': [],
        'unlocked_skills': [{'name': 'foraging', 'level': 1}],
        'requirements': {},
        'weight': 8,
        'tags': ['negative', 'uncommon', 'survival']
    },
    'normal_early_childhood': {
        'name': 'Typical Childhood',
        'description': 'You played with other children and learned basic skills.',
        'type': 'normal',
        'stat_changes': {'DEX': 3, 'persuasion': 2},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 35,
        'tags': ['neutral', 'common']
    }
}

MIDDLE_CHILDHOOD_EVENTS = {
    'awakened_third_eye': {
        'name': 'Third Eye Awakened',
        'description': 'A traumatic event opened your inner sight, allowing you to see beyond the physical realm.',
        'type': 'awakening',
        'stat_changes': {'perception': 50, 'magic_affinity': 30, 'INT': 20},
        'new_traits': ['third_eye_opened'],
        'unlocked_skills': [{'name': 'magical_sight', 'level': 1}],
        'requirements': {'blind': True},  # Special requirement for blind characters
        'weight': 1,
        'tags': ['positive', 'legendary', 'mystic']
    },
    'apprenticed_to_mage': {
        'name': 'Apprenticed to Local Mage',
        'description': 'A wizard took you as an apprentice, teaching you the basics of magic.',
        'type': 'mentorship',
        'stat_changes': {'INT': 10, 'WILL': 15, 'magic_affinity': 25},
        'new_traits': [],
        'unlocked_skills': [{'name': 'spellcraft', 'level': 1}],
        'requirements': {'magic_affinity': {'min': 70}, 'INT': {'min': 25}},
        'weight': 4,
        'tags': ['positive', 'rare', 'magic']
    },
    'bullied_by_peers': {
        'name': 'Bullied by Other Children',
        'description': 'Other children mocked and tormented you, hardening your resolve.',
        'type': 'trauma',
        'stat_changes': {'WILL': 8, 'persuasion': -10, 'beauty': -5},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 15,
        'tags': ['negative', 'common', 'social']
    },
    'showed_combat_talent': {
        'name': 'Showed Combat Talent',
        'description': 'You displayed natural fighting ability during play combat.',
        'type': 'skill_discovery',
        'stat_changes': {'STR': 8, 'DEX': 5},
        'new_traits': [],
        'unlocked_skills': [{'name': 'swordsmanship', 'level': 1}],
        'requirements': {'STR': {'min': 25}, 'DEX': {'min': 20}},
        'weight': 10,
        'tags': ['positive', 'uncommon', 'combat']
    },
    'contracted_plague': {
        'name': 'Survived Deadly Plague',
        'description': 'A terrible disease swept through your village. You survived, but bear the scars.',
        'type': 'illness',
        'stat_changes': {'immune_system': 40, 'VIT': 10, 'beauty': -30, 'STR': -10},
        'new_traits': ['plague_survivor'],
        'unlocked_skills': [],
        'requirements': {'immune_system': {'min': 80}},
        'weight': 3,
        'tags': ['negative', 'rare', 'health']
    },
    'normal_middle_childhood': {
        'name': 'Steady Growth',
        'description': 'You continued to grow and learn at a normal pace.',
        'type': 'normal',
        'stat_changes': {'STR': 4, 'DEX': 3, 'INT': 2},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 30,
        'tags': ['neutral', 'common']
    }
}

ADOLESCENCE_EVENTS = {
    'berserker_awakening': {
        'name': 'Berserker Rage Awakened',
        'description': 'In a moment of extreme anger, you entered a terrifying battle trance.',
        'type': 'awakening',
        'stat_changes': {'STR': 20, 'WILL': 15, 'persuasion': -20},
        'new_traits': ['berserker_rage'],
        'unlocked_skills': [{'name': 'berserker_combat', 'level': 1}],
        'requirements': {'STR': {'min': 40}, 'WILL': {'min': 30}},
        'weight': 2,
        'tags': ['negative', 'rare', 'combat']
    },
    'first_love': {
        'name': 'First Love',
        'description': 'You experienced the joy and pain of first love, learning about the heart.',
        'type': 'social',
        'stat_changes': {'beauty': 8, 'persuasion': 12, 'WILL': -5},
        'new_traits': [],
        'unlocked_skills': [{'name': 'seduction', 'level': 1}],
        'requirements': {'beauty': {'min': 80}},
        'weight': 20,
        'tags': ['positive', 'common', 'social']
    },
    'cursed_by_witch': {
        'name': 'Cursed by Vengeful Witch',
        'description': 'You angered a dark sorceress who placed a terrible curse upon your bloodline.',
        'type': 'curse',
        'stat_changes': {'chaos_resist': -20, 'holy_resist': -15, 'estimated_lifespan': -20},
        'new_traits': ['cursed_blood'],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 3,
        'tags': ['negative', 'rare', 'curse']
    },
    'joined_thieves_guild': {
        'name': 'Joined Thieves Guild',
        'description': 'You fell in with criminals and learned the arts of stealth and deception.',
        'type': 'criminal',
        'stat_changes': {'DEX': 12, 'persuasion': -8},
        'new_traits': [],
        'unlocked_skills': [{'name': 'stealth', 'level': 2}, {'name': 'deception', 'level': 1}],
        'requirements': {'DEX': {'min': 30}},
        'weight': 8,
        'tags': ['negative', 'uncommon', 'criminal']
    },
    'divine_vision': {
        'name': 'Received Divine Vision',
        'description': 'The gods spoke to you in a vision, blessing you with holy purpose.',
        'type': 'blessing',
        'stat_changes': {'WILL': 20, 'holy_resist': 25, 'chaos_resist': 15},
        'new_traits': ['blessed'],
        'unlocked_skills': [{'name': 'divine_magic', 'level': 1}],
        'requirements': {'WILL': {'min': 40}},
        'weight': 2,
        'tags': ['positive', 'rare', 'divine']
    },
    'coming_of_age': {
        'name': 'Coming of Age Ceremony',
        'description': 'You participated in your culture\'s traditional coming of age ritual.',
        'type': 'milestone',
        'stat_changes': {'WILL': 5, 'persuasion': 3},
        'new_traits': [],
        'unlocked_skills': [],
        'requirements': {},
        'weight': 25,
        'tags': ['neutral', 'common', 'cultural']
    }
}

# Life stage definitions
LIFE_STAGES = {
    'infancy': {
        'name': 'Infancy',
        'age_range': (0, 1),
        'events': INFANCY_EVENTS,
        'stat_growth': {'STR': 1, 'DEX': 1, 'VIT': 2, 'INT': 1},
        'event_chance': 0.3
    },
    'toddlerhood': {
        'name': 'Toddlerhood', 
        'age_range': (1, 3),
        'events': TODDLERHOOD_EVENTS,
        'stat_growth': {'STR': 2, 'DEX': 3, 'VIT': 2, 'INT': 2, 'SPE': 2},
        'event_chance': 0.4
    },
    'early_childhood': {
        'name': 'Early Childhood',
        'age_range': (3, 6),
        'events': EARLY_CHILDHOOD_EVENTS,
        'stat_growth': {'STR': 2, 'DEX': 2, 'VIT': 2, 'INT': 3, 'SPE': 1, 'WILL': 1},
        'event_chance': 0.5
    },
    'middle_childhood': {
        'name': 'Middle Childhood',
        'age_range': (6, 12),
        'events': MIDDLE_CHILDHOOD_EVENTS,
        'stat_growth': {'STR': 3, 'DEX': 2, 'VIT': 2, 'INT': 4, 'SPE': 2, 'WILL': 2},
        'event_chance': 0.6
    },
    'adolescence': {
        'name': 'Adolescence',
        'age_range': (12, 18),
        'events': ADOLESCENCE_EVENTS,
        'stat_growth': {'STR': 4, 'DEX': 3, 'VIT': 3, 'INT': 3, 'SPE': 2, 'WILL': 3},
        'event_chance': 0.7
    }
}

# Special milestone ages for enhanced event chances
MILESTONE_AGES = [1, 3, 6, 12, 18]

# Event type weights for random selection
EVENT_TYPE_WEIGHTS = {
    'normal': 50,
    'positive': 20,
    'negative': 15,
    'awakening': 3,
    'mentorship': 5,
    'trauma': 4,
    'blessing': 2,
    'curse': 1
}
