# One Life Isekai - Main Menu System

A dark fantasy single-life RPG where death is final and every choice matters.

## 🎮 Features Implemented

### Main Menu
- **Start Game** - Triggers character generation (with confirmation dialog)
- **Load Lore Archive** - Scrollable world-building content viewer
- **Options** - Comprehensive settings menu
- **Exit Game** - Safe exit with confirmation

### Options Menu
- **Audio Controls**: Master, Music, and SFX volume sliders (0-100)
- **Display Mode**: Fullscreen/Windowed toggle
- **Text Speed**: Slow/Normal/Fast cycling
- **Settings Persistence**: Auto-saves to `settings.json`

### Lore Archive
- **Scrollable Content**: Mouse wheel, keyboard, or button navigation
- **Rich Text**: Formatted world-building content
- **Read-Only**: Immersive lore experience

### UI Design
- **Gothic Theme**: Dark fantasy aesthetic with muted colors
- **Parchment Textures**: Aged, atmospheric interface
- **Gold Accents**: Elegant highlighting and hover effects
- **Confirmation Dialogs**: Critical action warnings

## 🚀 Quick Start

1. **Install Dependencies**:
   ```bash
   pip install pygame
   ```

2. **Run the Game**:
   ```bash
   python main.py
   ```

3. **Optional Assets** (for enhanced experience):
   - Place background image at: `assets/backgrounds/title_bg.jpg`
   - Place music file at: `assets/music/main_theme.ogg`
   - Place fonts at: `assets/fonts/serif.ttf` and `assets/fonts/gothic.ttf`

## 📁 Project Structure

```
One Life Isekai/
├── main.py                 # Entry point
├── requirements.txt        # Dependencies
├── settings.json          # User settings (auto-generated)
├── utils/
│   └── constants.py       # Game constants and theme
├── config/
│   └── settings.py        # Settings management
├── ui/
│   ├── menu_system.py     # Main menu controller
│   ├── components.py      # Reusable UI elements
│   ├── options_menu.py    # Settings interface
│   └── lore_archive.py    # Lore viewer
└── assets/               # Game assets (optional)
    ├── backgrounds/
    ├── music/
    └── fonts/
```

## 🎨 Theme & Aesthetics

- **Color Palette**: Deep purples, aged parchment, antique gold
- **Typography**: Serif fonts for immersion
- **Visual Style**: Gothic, fatalistic, atmospheric
- **User Experience**: Deliberate, weighty decisions

## ⚙️ Technical Details

- **Framework**: Pygame 2.5.0+
- **Resolution**: 1024x768 (configurable)
- **Settings**: JSON persistence
- **Modular Design**: Easy to extend with new menus

## 🔮 Game Philosophy

*"In Aethermoor, death is not merely an ending—it is the ultimate truth that gives meaning to every breath, every choice, every moment of existence."*

This main menu reflects the game's core philosophy:
- **Irreversible Consequences**: Confirmation dialogs for critical actions
- **Atmospheric Immersion**: Gothic theme and rich lore
- **Respectful Pacing**: No rushing, deliberate interactions

## 🛠️ Development Notes

The main menu system is complete and ready for integration with the actual game logic. Key integration points:

1. **Character Generation**: Hook into the "Start Game" confirmation
2. **Game World**: Initialize after character creation
3. **Save System**: Extend settings manager for game saves
4. **Audio System**: Integrate with existing volume controls

## 🎯 Next Steps

1. Implement AI-driven character generation
2. Create the game world and life simulation
3. Add save/load functionality
4. Integrate with the main menu system

---

**Version**: v0.1 – Prelude to Fate  
**Status**: Main Menu Complete ✅
