"""
Background History Generator for One Life Isekai
Generates life events from birth to age 18 based on character traits and stats
"""

import random
import copy
from typing import Dict, List, Any, Optional
from life_events import LIFE_STAGES, MILESTONE_AGES, EVENT_TYPE_WEIGHTS
from traits import ALL_TRAITS
from skills import ALL_SKILLS


class BackgroundHistoryGenerator:
    def __init__(self, character_profile: Dict[str, Any]):
        self.character = copy.deepcopy(character_profile)
        self.background_history = []
        self.used_event_ids = set()
        self.current_age = 0
        
    def check_event_requirements(self, event: Dict[str, Any]) -> bool:
        """Check if character meets event requirements"""
        requirements = event.get('requirements', {})
        
        for req_key, req_value in requirements.items():
            # Special trait requirements
            if req_key in ['blind', 'deaf', 'cursed_blood']:
                if req_key not in [trait.get('id', '') for trait in self.character.get('traits', [])]:
                    return False
                continue
            
            # Stat requirements
            char_value = self.character['effective_stats'].get(req_key, 0)
            
            if 'min' in req_value and char_value < req_value['min']:
                return False
            if 'max' in req_value and char_value > req_value['max']:
                return False
        
        return True
    
    def get_weighted_event(self, events: Dict[str, Any]) -> Optional[str]:
        """Select a random event based on weights and requirements"""
        available_events = {}
        
        for event_id, event_data in events.items():
            if event_id not in self.used_event_ids and self.check_event_requirements(event_data):
                available_events[event_id] = event_data
        
        if not available_events:
            return None
        
        # Calculate total weight
        total_weight = sum(event['weight'] for event in available_events.values())
        if total_weight == 0:
            return None
        
        # Select random event
        random_value = random.uniform(0, total_weight)
        current_weight = 0
        
        for event_id, event_data in available_events.items():
            current_weight += event_data['weight']
            if random_value <= current_weight:
                return event_id
        
        return list(available_events.keys())[-1]
    
    def apply_event_effects(self, event: Dict[str, Any]) -> None:
        """Apply event effects to character"""
        # Apply stat changes
        for stat, change in event.get('stat_changes', {}).items():
            if stat in self.character['effective_stats']:
                self.character['effective_stats'][stat] += change
            else:
                self.character['effective_stats'][stat] = change
        
        # Add new traits
        for trait_id in event.get('new_traits', []):
            if trait_id in ALL_TRAITS:
                trait_data = ALL_TRAITS[trait_id].copy()
                trait_data['id'] = trait_id
                trait_data['source'] = f'event_age_{self.current_age}'
                self.character.setdefault('traits', []).append(trait_data)
                
                # Apply trait effects
                for stat, effect in trait_data.get('effects', {}).items():
                    if stat in self.character['effective_stats']:
                        self.character['effective_stats'][stat] += effect
                    else:
                        self.character['effective_stats'][stat] = effect
        
        # Add new skills
        for skill_data in event.get('unlocked_skills', []):
            skill_id = skill_data['name']
            skill_level = skill_data.get('level', 1)
            
            if skill_id in ALL_SKILLS:
                skill_entry = {
                    'id': skill_id,
                    'name': ALL_SKILLS[skill_id]['name'],
                    'level': skill_level,
                    'source': f'event_age_{self.current_age}'
                }
                self.character.setdefault('skills', []).append(skill_entry)
    
    def apply_natural_growth(self, life_stage: Dict[str, Any]) -> Dict[str, int]:
        """Apply natural stat growth for the life stage"""
        growth_applied = {}
        base_growth = life_stage.get('stat_growth', {})
        
        for stat, base_amount in base_growth.items():
            # Add some randomness to growth
            growth_variance = random.randint(-1, 2)
            actual_growth = max(0, base_amount + growth_variance)
            
            # Apply racial and trait modifiers
            if stat == 'STR' and 'giant_blood' in [t.get('id', '') for t in self.character.get('traits', [])]:
                actual_growth = int(actual_growth * 1.5)
            
            if actual_growth > 0:
                self.character['effective_stats'][stat] = self.character['effective_stats'].get(stat, 0) + actual_growth
                growth_applied[stat] = actual_growth
        
        return growth_applied
    
    def get_life_stage_for_age(self, age: int) -> Optional[str]:
        """Get the life stage for a given age"""
        for stage_id, stage_data in LIFE_STAGES.items():
            min_age, max_age = stage_data['age_range']
            if min_age <= age < max_age:
                return stage_id
        return None
    
    def generate_year_events(self, age: int) -> List[Dict[str, Any]]:
        """Generate events for a specific year"""
        self.current_age = age
        year_events = []
        
        # Get life stage
        life_stage_id = self.get_life_stage_for_age(age)
        if not life_stage_id:
            return year_events
        
        life_stage = LIFE_STAGES[life_stage_id]
        
        # Apply natural growth
        growth_applied = self.apply_natural_growth(life_stage)
        if growth_applied:
            growth_event = {
                'age': age,
                'life_stage': life_stage['name'],
                'event': 'Natural Growth',
                'type': 'growth',
                'effects': growth_applied,
                'traits_gained': [],
                'skills_gained': []
            }
            year_events.append(growth_event)
        
        # Check for events
        event_chance = life_stage['event_chance']
        
        # Milestone ages have higher event chances
        if age in MILESTONE_AGES:
            event_chance *= 1.5
        
        # Roll for events (0-2 events per year)
        num_events = 0
        if random.random() < event_chance:
            num_events = 1
            if random.random() < 0.3:  # 30% chance for second event
                num_events = 2
        
        # Generate events
        for _ in range(num_events):
            event_id = self.get_weighted_event(life_stage['events'])
            if event_id:
                event_data = life_stage['events'][event_id]
                
                # Create event record
                event_record = {
                    'age': age,
                    'life_stage': life_stage['name'],
                    'event': event_data['name'],
                    'description': event_data['description'],
                    'type': event_data['type'],
                    'effects': event_data.get('stat_changes', {}),
                    'traits_gained': event_data.get('new_traits', []),
                    'skills_gained': event_data.get('unlocked_skills', [])
                }
                
                # Apply effects
                self.apply_event_effects(event_data)
                
                # Mark event as used
                self.used_event_ids.add(event_id)
                
                year_events.append(event_record)
        
        return year_events
    
    def generate_full_background(self) -> List[Dict[str, Any]]:
        """Generate complete background history from age 0 to 18"""
        self.background_history = []
        
        # Check lifespan limit
        max_age = min(18, self.character['effective_stats'].get('estimated_lifespan', 80))
        
        for age in range(0, max_age + 1):
            year_events = self.generate_year_events(age)
            self.background_history.extend(year_events)
            
            # Check if character died
            if age >= self.character['effective_stats'].get('estimated_lifespan', 80):
                death_event = {
                    'age': age,
                    'life_stage': 'death',
                    'event': 'Death from Natural Causes',
                    'description': 'Your time in this world has come to an end.',
                    'type': 'death',
                    'effects': {},
                    'traits_gained': [],
                    'skills_gained': []
                }
                self.background_history.append(death_event)
                break
        
        # Update character profile
        self.character['background_history'] = self.background_history
        
        return self.background_history
    
    def get_updated_character(self) -> Dict[str, Any]:
        """Get the character profile with applied background history"""
        return self.character
    
    def format_background_summary(self) -> str:
        """Format background history as a readable summary"""
        summary_lines = []
        summary_lines.append("📜 BACKGROUND HISTORY")
        summary_lines.append("=" * 50)
        
        current_stage = ""
        for event in self.background_history:
            if event['life_stage'] != current_stage:
                current_stage = event['life_stage']
                summary_lines.append(f"\n🔸 {current_stage.upper()}")
                summary_lines.append("-" * 30)
            
            age_text = f"Age {event['age']}"
            event_text = event['event']
            
            if event['type'] == 'growth':
                effects = ", ".join([f"+{v} {k}" for k, v in event['effects'].items()])
                summary_lines.append(f"  {age_text}: {event_text} ({effects})")
            else:
                summary_lines.append(f"  {age_text}: {event_text}")
                
                if event['effects']:
                    effects = ", ".join([f"{v:+d} {k}" for k, v in event['effects'].items()])
                    summary_lines.append(f"    Effects: {effects}")
                
                if event['traits_gained']:
                    traits = ", ".join(event['traits_gained'])
                    summary_lines.append(f"    Traits Gained: {traits}")
                
                if event['skills_gained']:
                    skills = ", ".join([f"{s['name']} (Level {s.get('level', 1)})" for s in event['skills_gained']])
                    summary_lines.append(f"    Skills Gained: {skills}")
        
        return "\n".join(summary_lines)
