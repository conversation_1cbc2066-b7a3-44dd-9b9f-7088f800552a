"""
Main menu system for One Life Isekai
Handles navigation between different menu screens
"""

import pygame
import os
from enum import Enum
from typing import Optional
from ui.components import <PERSON><PERSON>, ConfirmDialog
from ui.options_menu import OptionsMenu
from ui.lore_archive import LoreArchive
from config.settings import settings
from utils.constants import (
    COLORS, SCREEN_WIDTH, SCREEN_HEIGHT, BUTTON_WIDTH, BUTTON_HEIGHT, 
    BUTTON_SPACING, MENU_START_Y, ASSETS, VERSION
)


class MenuState(Enum):
    MAIN = "main"
    OPTIONS = "options"
    LORE = "lore"


class MainMenuSystem:
    def __init__(self):
        self.current_state = MenuState.MAIN
        self.running = True
        self.start_game = False
        
        # Initialize pygame
        pygame.init()
        pygame.mixer.init()
        
        # Set up display
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("One Life Isekai")
        self.clock = pygame.time.Clock()
        
        # Load fonts
        self._load_fonts()
        
        # Load assets
        self._load_assets()
        
        # Create menu components
        self._create_main_menu()
        self.options_menu = OptionsMenu(self.font_medium, self._back_to_main)
        self.lore_archive = LoreArchive(self.font_small, self._back_to_main)
        
        # Dialog state
        self.active_dialog: Optional[ConfirmDialog] = None
        
        # Start background music
        self._start_music()
    
    def _load_fonts(self) -> None:
        """Load fonts with fallbacks"""
        try:
            # Try to load custom fonts
            self.font_large = pygame.font.Font(ASSETS['fonts']['serif'], 32)
            self.font_medium = pygame.font.Font(ASSETS['fonts']['serif'], 24)
            self.font_small = pygame.font.Font(ASSETS['fonts']['serif'], 18)
        except (FileNotFoundError, pygame.error):
            # Fallback to system fonts
            self.font_large = pygame.font.Font(None, 32)
            self.font_medium = pygame.font.Font(None, 24)
            self.font_small = pygame.font.Font(None, 18)
    
    def _load_assets(self) -> None:
        """Load background and other assets"""
        try:
            if os.path.exists(ASSETS['background']):
                self.background = pygame.image.load(ASSETS['background'])
                self.background = pygame.transform.scale(self.background, (SCREEN_WIDTH, SCREEN_HEIGHT))
            else:
                # Create a gradient background as fallback
                self.background = self._create_gradient_background()
        except pygame.error:
            self.background = self._create_gradient_background()
    
    def _create_gradient_background(self) -> pygame.Surface:
        """Create a dark gradient background"""
        background = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
        for y in range(SCREEN_HEIGHT):
            # Create a vertical gradient from dark purple to black
            ratio = y / SCREEN_HEIGHT
            r = int(COLORS['background'][0] * (1 - ratio * 0.5))
            g = int(COLORS['background'][1] * (1 - ratio * 0.5))
            b = int(COLORS['background'][2] * (1 - ratio * 0.3))
            color = (r, g, b)
            pygame.draw.line(background, color, (0, y), (SCREEN_WIDTH, y))
        return background
    
    def _start_music(self) -> None:
        """Start background music if available"""
        try:
            if os.path.exists(ASSETS['music']):
                pygame.mixer.music.load(ASSETS['music'])
                volume = settings.get_music_volume() / 100.0 * settings.get_master_volume() / 100.0
                pygame.mixer.music.set_volume(volume)
                pygame.mixer.music.play(-1)  # Loop indefinitely
        except pygame.error:
            print("Background music not available")
    
    def _create_main_menu(self) -> None:
        """Create main menu buttons"""
        center_x = (SCREEN_WIDTH - BUTTON_WIDTH) // 2
        
        self.start_button = Button(
            center_x, MENU_START_Y, BUTTON_WIDTH, BUTTON_HEIGHT,
            "Start Game", self.font_medium, self._show_start_confirmation
        )
        
        self.lore_button = Button(
            center_x, MENU_START_Y + (BUTTON_HEIGHT + BUTTON_SPACING),
            BUTTON_WIDTH, BUTTON_HEIGHT,
            "Load Lore Archive", self.font_medium, self._show_lore
        )
        
        self.options_button = Button(
            center_x, MENU_START_Y + 2 * (BUTTON_HEIGHT + BUTTON_SPACING),
            BUTTON_WIDTH, BUTTON_HEIGHT,
            "Options", self.font_medium, self._show_options
        )
        
        self.exit_button = Button(
            center_x, MENU_START_Y + 3 * (BUTTON_HEIGHT + BUTTON_SPACING),
            BUTTON_WIDTH, BUTTON_HEIGHT,
            "Exit Game", self.font_medium, self._show_exit_confirmation
        )
        
        # Create title text
        self.title_surface = self.font_large.render("ONE LIFE ISEKAI", True, COLORS['gold'])
        self.subtitle_surface = self.font_medium.render("Where Death is Final", True, COLORS['text_secondary'])
        
        # Create version text
        self.version_surface = self.font_small.render(VERSION, True, COLORS['text_secondary'])
    
    def _show_start_confirmation(self) -> None:
        """Show confirmation dialog for starting the game"""
        self.active_dialog = ConfirmDialog(
            "Warning",
            "You only get one life. Once you proceed, there's no going back.",
            self.font_medium,
            self._start_game,
            self._close_dialog
        )
    
    def _show_exit_confirmation(self) -> None:
        """Show confirmation dialog for exiting"""
        self.active_dialog = ConfirmDialog(
            "Exit Game",
            "Are you sure you want to leave your fate unfinished?",
            self.font_medium,
            self._exit_game,
            self._close_dialog
        )
    
    def _start_game(self) -> None:
        """Start the actual game"""
        self.start_game = True
        self.running = False
        self._close_dialog()
    
    def _exit_game(self) -> None:
        """Exit the application"""
        self.running = False
        self._close_dialog()
    
    def _close_dialog(self) -> None:
        """Close the active dialog"""
        self.active_dialog = None
    
    def _show_options(self) -> None:
        """Switch to options menu"""
        self.current_state = MenuState.OPTIONS
    
    def _show_lore(self) -> None:
        """Switch to lore archive"""
        self.current_state = MenuState.LORE
    
    def _back_to_main(self) -> None:
        """Return to main menu"""
        self.current_state = MenuState.MAIN
        # Save settings when returning from options
        settings.save_settings()
    
    def handle_event(self, event: pygame.event.Event) -> None:
        """Handle input events"""
        if event.type == pygame.QUIT:
            self.running = False
        
        # Handle dialog events first
        if self.active_dialog:
            if self.active_dialog.handle_event(event):
                self.active_dialog = None
            return
        
        # Handle menu-specific events
        if self.current_state == MenuState.MAIN:
            self.start_button.handle_event(event)
            self.lore_button.handle_event(event)
            self.options_button.handle_event(event)
            self.exit_button.handle_event(event)
        elif self.current_state == MenuState.OPTIONS:
            self.options_menu.handle_event(event)
        elif self.current_state == MenuState.LORE:
            self.lore_archive.handle_event(event)
    
    def draw(self) -> None:
        """Draw the current menu state"""
        # Draw background
        self.screen.blit(self.background, (0, 0))
        
        if self.current_state == MenuState.MAIN:
            self._draw_main_menu()
        elif self.current_state == MenuState.OPTIONS:
            self.options_menu.draw(self.screen)
        elif self.current_state == MenuState.LORE:
            self.lore_archive.draw(self.screen)
        
        # Draw dialog on top if active
        if self.active_dialog:
            # Darken background
            overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            overlay.set_alpha(128)
            overlay.fill((0, 0, 0))
            self.screen.blit(overlay, (0, 0))
            
            # Draw dialog
            self.active_dialog.draw(self.screen)
        
        pygame.display.flip()
    
    def _draw_main_menu(self) -> None:
        """Draw the main menu"""
        # Draw title
        title_rect = self.title_surface.get_rect()
        title_x = (SCREEN_WIDTH - title_rect.width) // 2
        self.screen.blit(self.title_surface, (title_x, 100))
        
        # Draw subtitle
        subtitle_rect = self.subtitle_surface.get_rect()
        subtitle_x = (SCREEN_WIDTH - subtitle_rect.width) // 2
        self.screen.blit(self.subtitle_surface, (subtitle_x, 150))
        
        # Draw buttons
        self.start_button.draw(self.screen)
        self.lore_button.draw(self.screen)
        self.options_button.draw(self.screen)
        self.exit_button.draw(self.screen)
        
        # Draw version in corner
        self.screen.blit(self.version_surface, (10, SCREEN_HEIGHT - 30))
    
    def run(self) -> bool:
        """Main menu loop, returns True if game should start"""
        while self.running:
            # Handle events
            for event in pygame.event.get():
                self.handle_event(event)
            
            # Draw
            self.draw()
            
            # Control frame rate
            self.clock.tick(60)
        
        # Cleanup
        pygame.mixer.music.stop()
        settings.save_settings()
        
        return self.start_game
