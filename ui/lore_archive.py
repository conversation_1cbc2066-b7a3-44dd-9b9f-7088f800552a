"""
Lore Archive viewer for One Life Isekai
Scrollable read-only window for world-building content
"""

import pygame
from typing import Callable, List
from ui.components import Button
from utils.constants import COLORS, SCREEN_WIDTH, SCREEN_HEIGHT, LORE_CONTENT


class LoreArchive:
    def __init__(self, font: pygame.font.Font, back_callback: Callable):
        self.font = font
        self.back_callback = back_callback
        
        # Scrolling setup
        self.scroll_y = 0
        self.scroll_speed = 30
        self.line_height = font.get_height() + 5
        
        # Content area
        self.content_rect = pygame.Rect(50, 100, SCREEN_WIDTH - 100, SCREEN_HEIGHT - 200)
        self.max_width = self.content_rect.width - 40  # Padding
        
        # Process lore content into wrapped lines
        self.lines = self._wrap_text(LORE_CONTENT)
        self.total_height = len(self.lines) * self.line_height
        self.max_scroll = max(0, self.total_height - self.content_rect.height)
        
        # Create UI elements
        self._create_buttons()
        self._create_labels()
    
    def _wrap_text(self, text: str) -> List[str]:
        """Wrap text to fit within the content area"""
        lines = []
        paragraphs = text.strip().split('\n\n')
        
        for paragraph in paragraphs:
            if paragraph.strip():
                # Handle paragraph titles (all caps lines)
                if paragraph.isupper() and len(paragraph) < 50:
                    lines.append("")  # Empty line before title
                    lines.append(paragraph.strip())
                    lines.append("")  # Empty line after title
                else:
                    # Wrap regular text
                    words = paragraph.split()
                    current_line = ""
                    
                    for word in words:
                        test_line = current_line + word + " "
                        if self.font.size(test_line)[0] <= self.max_width:
                            current_line = test_line
                        else:
                            if current_line:
                                lines.append(current_line.strip())
                            current_line = word + " "
                    
                    if current_line:
                        lines.append(current_line.strip())
                    
                    lines.append("")  # Empty line after paragraph
        
        return lines
    
    def _create_buttons(self) -> None:
        """Create navigation buttons"""
        button_width = 150
        button_height = 40
        
        self.back_button = Button(
            50, SCREEN_HEIGHT - 80, button_width, button_height,
            "Back to Menu", self.font, self.back_callback
        )
        
        # Scroll buttons
        self.scroll_up_button = Button(
            SCREEN_WIDTH - 200, 100, button_width, button_height,
            "Scroll Up", self.font, self._scroll_up
        )
        
        self.scroll_down_button = Button(
            SCREEN_WIDTH - 200, 150, button_width, button_height,
            "Scroll Down", self.font, self._scroll_down
        )
    
    def _create_labels(self) -> None:
        """Create title label"""
        self.title_surface = self.font.render("LORE ARCHIVE", True, COLORS['gold'])
    
    def _scroll_up(self) -> None:
        """Scroll content up"""
        self.scroll_y = max(0, self.scroll_y - self.scroll_speed)
    
    def _scroll_down(self) -> None:
        """Scroll content down"""
        self.scroll_y = min(self.max_scroll, self.scroll_y + self.scroll_speed)
    
    def handle_event(self, event: pygame.event.Event) -> None:
        """Handle input events"""
        # Handle mouse wheel scrolling
        if event.type == pygame.MOUSEWHEEL:
            if event.y > 0:  # Scroll up
                self._scroll_up()
            elif event.y < 0:  # Scroll down
                self._scroll_down()
        
        # Handle keyboard scrolling
        elif event.type == pygame.KEYDOWN:
            if event.key == pygame.K_UP:
                self._scroll_up()
            elif event.key == pygame.K_DOWN:
                self._scroll_down()
            elif event.key == pygame.K_PAGEUP:
                self.scroll_y = max(0, self.scroll_y - self.content_rect.height // 2)
            elif event.key == pygame.K_PAGEDOWN:
                self.scroll_y = min(self.max_scroll, self.scroll_y + self.content_rect.height // 2)
            elif event.key == pygame.K_HOME:
                self.scroll_y = 0
            elif event.key == pygame.K_END:
                self.scroll_y = self.max_scroll
        
        # Handle buttons
        self.back_button.handle_event(event)
        self.scroll_up_button.handle_event(event)
        self.scroll_down_button.handle_event(event)
    
    def draw(self, surface: pygame.Surface) -> None:
        """Draw the lore archive"""
        # Clear background
        surface.fill(COLORS['background'])
        
        # Draw title
        title_rect = self.title_surface.get_rect()
        title_x = (SCREEN_WIDTH - title_rect.width) // 2
        surface.blit(self.title_surface, (title_x, 30))
        
        # Draw content background (parchment)
        pygame.draw.rect(surface, COLORS['parchment'], self.content_rect)
        pygame.draw.rect(surface, COLORS['border'], self.content_rect, 3)
        
        # Create clipping rect for scrollable content
        clip_rect = self.content_rect.copy()
        clip_rect.inflate_ip(-20, -20)  # Add padding
        
        # Save current clipping area
        old_clip = surface.get_clip()
        surface.set_clip(clip_rect)
        
        # Draw text lines
        start_line = max(0, self.scroll_y // self.line_height)
        end_line = min(len(self.lines), start_line + (clip_rect.height // self.line_height) + 2)
        
        for i in range(start_line, end_line):
            line = self.lines[i]
            if line.strip():  # Don't render empty lines
                y_pos = clip_rect.y + (i * self.line_height) - self.scroll_y
                
                # Choose color based on content
                if line.isupper() and len(line) < 50:  # Titles
                    color = COLORS['gold']
                elif line.startswith('•'):  # Bullet points
                    color = COLORS['text_secondary']
                else:  # Regular text
                    color = COLORS['text_primary']
                
                text_surface = self.font.render(line, True, color)
                surface.blit(text_surface, (clip_rect.x + 10, y_pos))
        
        # Restore clipping area
        surface.set_clip(old_clip)
        
        # Draw scroll indicator
        if self.max_scroll > 0:
            indicator_height = max(20, (clip_rect.height * clip_rect.height) // self.total_height)
            indicator_y = clip_rect.y + (self.scroll_y * clip_rect.height) // self.total_height
            indicator_rect = pygame.Rect(clip_rect.right - 10, indicator_y, 8, indicator_height)
            pygame.draw.rect(surface, COLORS['gold'], indicator_rect)
        
        # Draw buttons
        self.back_button.draw(surface)
        self.scroll_up_button.draw(surface)
        self.scroll_down_button.draw(surface)
        
        # Draw scroll instructions
        if self.max_scroll > 0:
            instruction_text = "Use mouse wheel, arrow keys, or buttons to scroll"
            instruction_surface = pygame.font.Font(None, 20).render(instruction_text, True, COLORS['text_secondary'])
            instruction_rect = instruction_surface.get_rect()
            instruction_x = (SCREEN_WIDTH - instruction_rect.width) // 2
            surface.blit(instruction_surface, (instruction_x, SCREEN_HEIGHT - 25))
