"""
Options/Settings menu for One Life Isekai
Handles audio, display, and gameplay settings
"""

import pygame
from typing import Callable
from ui.components import <PERSON>ton, Slider
from config.settings import settings
from utils.constants import COLORS, SCREEN_WIDTH, SCREEN_HEIGHT


class OptionsMenu:
    def __init__(self, font: pygame.font.Font, back_callback: Callable):
        self.font = font
        self.back_callback = back_callback
        
        # Create UI elements
        self._create_sliders()
        self._create_buttons()
        self._create_labels()
    
    def _create_sliders(self) -> None:
        """Create volume sliders"""
        slider_x = SCREEN_WIDTH // 2 - 150
        slider_width = 300
        slider_height = 30
        
        self.master_slider = Slider(
            slider_x, 200, slider_width, slider_height,
            0, 100, settings.get_master_volume()
        )
        
        self.music_slider = Slider(
            slider_x, 260, slider_width, slider_height,
            0, 100, settings.get_music_volume()
        )
        
        self.sfx_slider = Slider(
            slider_x, 320, slider_width, slider_height,
            0, 100, settings.get_sfx_volume()
        )
    
    def _create_buttons(self) -> None:
        """Create option buttons"""
        button_width = 200
        button_height = 40
        center_x = SCREEN_WIDTH // 2 - button_width // 2
        
        # Fullscreen toggle
        fullscreen_text = "Windowed" if settings.get_fullscreen() else "Fullscreen"
        self.fullscreen_button = Button(
            center_x, 400, button_width, button_height,
            fullscreen_text, self.font, self._toggle_fullscreen
        )
        
        # Text speed cycle
        self.text_speed_button = Button(
            center_x, 460, button_width, button_height,
            f"Text Speed: {settings.get_text_speed()}", self.font, self._cycle_text_speed
        )
        
        # Save settings
        self.save_button = Button(
            center_x, 540, button_width, button_height,
            "Save Settings", self.font, self._save_settings
        )
        
        # Back button
        self.back_button = Button(
            center_x, 600, button_width, button_height,
            "Back to Menu", self.font, self.back_callback
        )
    
    def _create_labels(self) -> None:
        """Create text labels"""
        self.title_surface = self.font.render("OPTIONS", True, COLORS['gold'])
        self.master_label = self.font.render("Master Volume:", True, COLORS['text_primary'])
        self.music_label = self.font.render("Music Volume:", True, COLORS['text_primary'])
        self.sfx_label = self.font.render("Sound FX Volume:", True, COLORS['text_primary'])
        self.display_label = self.font.render("Display Mode:", True, COLORS['text_primary'])
    
    def _toggle_fullscreen(self) -> None:
        """Toggle fullscreen mode"""
        current = settings.get_fullscreen()
        settings.set_fullscreen(not current)
        
        # Update button text
        new_text = "Windowed" if settings.get_fullscreen() else "Fullscreen"
        self.fullscreen_button.text = new_text
        self.fullscreen_button.text_surface = self.font.render(new_text, True, COLORS['text_primary'])
        self.fullscreen_button.text_hover_surface = self.font.render(new_text, True, COLORS['gold_hover'])
    
    def _cycle_text_speed(self) -> None:
        """Cycle through text speed options"""
        speeds = ['Slow', 'Normal', 'Fast']
        current = settings.get_text_speed()
        current_index = speeds.index(current) if current in speeds else 1
        new_index = (current_index + 1) % len(speeds)
        new_speed = speeds[new_index]
        
        settings.set_text_speed(new_speed)
        
        # Update button text
        new_text = f"Text Speed: {new_speed}"
        self.text_speed_button.text = new_text
        self.text_speed_button.text_surface = self.font.render(new_text, True, COLORS['text_primary'])
        self.text_speed_button.text_hover_surface = self.font.render(new_text, True, COLORS['gold_hover'])
    
    def _save_settings(self) -> None:
        """Save current settings to file"""
        settings.save_settings()
        print("Settings saved!")  # Could show a temporary message in the UI
    
    def handle_event(self, event: pygame.event.Event) -> None:
        """Handle input events"""
        # Handle sliders
        if self.master_slider.handle_event(event):
            settings.set_volume('master', self.master_slider.val)
        
        if self.music_slider.handle_event(event):
            settings.set_volume('music', self.music_slider.val)
        
        if self.sfx_slider.handle_event(event):
            settings.set_volume('sfx', self.sfx_slider.val)
        
        # Handle buttons
        self.fullscreen_button.handle_event(event)
        self.text_speed_button.handle_event(event)
        self.save_button.handle_event(event)
        self.back_button.handle_event(event)
    
    def draw(self, surface: pygame.Surface) -> None:
        """Draw the options menu"""
        # Clear background
        surface.fill(COLORS['background'])
        
        # Draw title
        title_rect = self.title_surface.get_rect()
        title_x = (SCREEN_WIDTH - title_rect.width) // 2
        surface.blit(self.title_surface, (title_x, 50))
        
        # Draw volume labels and sliders
        surface.blit(self.master_label, (SCREEN_WIDTH // 2 - 150, 170))
        surface.blit(self.music_label, (SCREEN_WIDTH // 2 - 150, 230))
        surface.blit(self.sfx_label, (SCREEN_WIDTH // 2 - 150, 290))
        
        self.master_slider.draw(surface)
        self.music_slider.draw(surface)
        self.sfx_slider.draw(surface)
        
        # Draw volume values
        master_val_text = self.font.render(str(self.master_slider.val), True, COLORS['text_secondary'])
        music_val_text = self.font.render(str(self.music_slider.val), True, COLORS['text_secondary'])
        sfx_val_text = self.font.render(str(self.sfx_slider.val), True, COLORS['text_secondary'])
        
        surface.blit(master_val_text, (SCREEN_WIDTH // 2 + 170, 205))
        surface.blit(music_val_text, (SCREEN_WIDTH // 2 + 170, 265))
        surface.blit(sfx_val_text, (SCREEN_WIDTH // 2 + 170, 325))
        
        # Draw display label
        surface.blit(self.display_label, (SCREEN_WIDTH // 2 - 100, 370))
        
        # Draw buttons
        self.fullscreen_button.draw(surface)
        self.text_speed_button.draw(surface)
        self.save_button.draw(surface)
        self.back_button.draw(surface)
