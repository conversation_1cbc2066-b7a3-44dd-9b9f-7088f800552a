"""
Reusable UI components for One Life Isekai
Gothic-themed buttons, sliders, and other interface elements
"""

import pygame
from typing import Callable, Optional, Tuple
from utils.constants import COLORS, FONT_SIZES


class Button:
    def __init__(self, x: int, y: int, width: int, height: int, text: str, 
                 font: pygame.font.Font, callback: Optional[Callable] = None):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.font = font
        self.callback = callback
        self.hovered = False
        self.pressed = False
        
        # Pre-render text surfaces
        self.text_surface = font.render(text, True, COLORS['text_primary'])
        self.text_hover_surface = font.render(text, True, COLORS['gold_hover'])
        
        # Center text in button
        text_rect = self.text_surface.get_rect()
        self.text_x = x + (width - text_rect.width) // 2
        self.text_y = y + (height - text_rect.height) // 2
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle mouse events, return True if button was clicked"""
        if event.type == pygame.MOUSEMOTION:
            self.hovered = self.rect.collidepoint(event.pos)
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1 and self.rect.collidepoint(event.pos):
                self.pressed = True
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1 and self.pressed and self.rect.collidepoint(event.pos):
                self.pressed = False
                if self.callback:
                    self.callback()
                return True
            self.pressed = False
        return False
    
    def draw(self, surface: pygame.Surface) -> None:
        """Draw the button"""
        # Button background
        color = COLORS['gold_hover'] if self.hovered else COLORS['gold']
        if self.pressed:
            color = COLORS['blood_red']
        
        # Draw button with border
        pygame.draw.rect(surface, color, self.rect)
        pygame.draw.rect(surface, COLORS['border'], self.rect, 3)
        
        # Draw text
        text_surf = self.text_hover_surface if self.hovered else self.text_surface
        surface.blit(text_surf, (self.text_x, self.text_y))


class Slider:
    def __init__(self, x: int, y: int, width: int, height: int, 
                 min_val: int = 0, max_val: int = 100, initial_val: int = 50):
        self.rect = pygame.Rect(x, y, width, height)
        self.min_val = min_val
        self.max_val = max_val
        self.val = initial_val
        self.dragging = False
        
        # Slider components
        self.track_rect = pygame.Rect(x, y + height//3, width, height//3)
        self.handle_size = height
        self.handle_rect = pygame.Rect(0, y, self.handle_size, height)
        self._update_handle_position()
    
    def _update_handle_position(self) -> None:
        """Update handle position based on current value"""
        ratio = (self.val - self.min_val) / (self.max_val - self.min_val)
        handle_x = self.track_rect.x + ratio * (self.track_rect.width - self.handle_size)
        self.handle_rect.x = int(handle_x)
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle mouse events, return True if value changed"""
        if event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1 and self.handle_rect.collidepoint(event.pos):
                self.dragging = True
        elif event.type == pygame.MOUSEBUTTONUP:
            if event.button == 1:
                self.dragging = False
        elif event.type == pygame.MOUSEMOTION and self.dragging:
            # Calculate new value based on mouse position
            mouse_x = event.pos[0]
            relative_x = mouse_x - self.track_rect.x
            ratio = max(0, min(1, relative_x / (self.track_rect.width - self.handle_size)))
            old_val = self.val
            self.val = int(self.min_val + ratio * (self.max_val - self.min_val))
            self._update_handle_position()
            return old_val != self.val
        return False
    
    def set_value(self, value: int) -> None:
        """Set slider value"""
        self.val = max(self.min_val, min(self.max_val, value))
        self._update_handle_position()
    
    def draw(self, surface: pygame.Surface) -> None:
        """Draw the slider"""
        # Draw track
        pygame.draw.rect(surface, COLORS['shadow'], self.track_rect)
        pygame.draw.rect(surface, COLORS['border'], self.track_rect, 2)
        
        # Draw handle
        pygame.draw.rect(surface, COLORS['gold'], self.handle_rect)
        pygame.draw.rect(surface, COLORS['border'], self.handle_rect, 2)


class ConfirmDialog:
    def __init__(self, title: str, message: str, font: pygame.font.Font, 
                 confirm_callback: Callable, cancel_callback: Optional[Callable] = None):
        self.title = title
        self.message = message
        self.font = font
        self.confirm_callback = confirm_callback
        self.cancel_callback = cancel_callback
        
        # Dialog dimensions
        self.width = 500
        self.height = 200
        self.x = (800 - self.width) // 2  # Assuming 800px screen width
        self.y = (600 - self.height) // 2  # Assuming 600px screen height
        
        self.rect = pygame.Rect(self.x, self.y, self.width, self.height)
        
        # Create buttons
        button_width = 120
        button_height = 40
        button_y = self.y + self.height - 60
        
        self.confirm_button = Button(
            self.x + self.width - button_width - 20, button_y,
            button_width, button_height, "Proceed", font, self._confirm
        )
        
        self.cancel_button = Button(
            self.x + 20, button_y,
            button_width, button_height, "Cancel", font, self._cancel
        )
    
    def _confirm(self) -> None:
        if self.confirm_callback:
            self.confirm_callback()
    
    def _cancel(self) -> None:
        if self.cancel_callback:
            self.cancel_callback()
    
    def handle_event(self, event: pygame.event.Event) -> bool:
        """Handle events, return True if dialog should close"""
        if self.confirm_button.handle_event(event):
            return True
        if self.cancel_button.handle_event(event):
            return True
        return False
    
    def draw(self, surface: pygame.Surface) -> None:
        """Draw the confirmation dialog"""
        # Draw background with border
        pygame.draw.rect(surface, COLORS['parchment'], self.rect)
        pygame.draw.rect(surface, COLORS['border'], self.rect, 3)
        
        # Draw title
        title_surface = self.font.render(self.title, True, COLORS['blood_red'])
        title_rect = title_surface.get_rect()
        title_x = self.x + (self.width - title_rect.width) // 2
        surface.blit(title_surface, (title_x, self.y + 20))
        
        # Draw message (word wrap if needed)
        words = self.message.split(' ')
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + word + " "
            if self.font.size(test_line)[0] < self.width - 40:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line.strip())
                current_line = word + " "
        if current_line:
            lines.append(current_line.strip())
        
        # Draw message lines
        line_height = self.font.get_height()
        start_y = self.y + 60
        for i, line in enumerate(lines):
            line_surface = self.font.render(line, True, COLORS['text_primary'])
            line_rect = line_surface.get_rect()
            line_x = self.x + (self.width - line_rect.width) // 2
            surface.blit(line_surface, (line_x, start_y + i * line_height))
        
        # Draw buttons
        self.confirm_button.draw(surface)
        self.cancel_button.draw(surface)
