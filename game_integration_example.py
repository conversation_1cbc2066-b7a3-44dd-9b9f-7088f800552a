#!/usr/bin/env python3
"""
Integration example showing how the character generator connects with the main game
"""

import pygame
import sys
from character_generator import CharacterGenerator
from ui.menu_system import MainMenuSystem


class GameWithCharacterGeneration:
    def __init__(self):
        self.character = None
        self.generator = CharacterGenerator()
    
    def start_character_generation(self):
        """Start the character generation process"""
        print("\n" + "🌟" * 30)
        print("WELCOME TO ONE LIFE ISEKAI")
        print("🌟" * 30)
        print("\nYou are about to begin your one and only life.")
        print("Every choice matters. Every trait is permanent.")
        print("There are no second chances.")
        
        input("\nPress Enter to discover your fate...")
        
        # Generate character
        self.character = self.generator.generate_full_character(display=True)
        
        print("\n" + "⚡" * 40)
        print("CHARACTER CREATION COMPLETE")
        print("⚡" * 40)
        
        return self.character
    
    def display_character_sheet(self):
        """Display the final character sheet"""
        if not self.character:
            print("No character generated yet!")
            return
        
        char = self.character
        race = char['race']
        gender = char['gender']
        
        print("\n" + "═" * 60)
        print("📜 CHARACTER SHEET")
        print("═" * 60)
        
        print(f"Name: [To be chosen in-game]")
        print(f"Race: {race['name']} ({race['rarity']}, {race['quality']})")
        print(f"Gender: {gender['name']}")
        print(f"Age: 0 (Newborn)")
        
        print("\n📊 CORE ATTRIBUTES:")
        for stat in ['STR', 'DEX', 'VIT', 'INT', 'SPE', 'WILL']:
            value = char['effective_stats'].get(stat, 0)
            print(f"  {stat}: {value}")
        
        print("\n🎭 PHYSICAL TRAITS:")
        print(f"  Height: {char['effective_stats'].get('height', 0)} cm")
        print(f"  Beauty: {char['effective_stats'].get('beauty', 0)}")
        print(f"  Expected Lifespan: {char['effective_stats'].get('estimated_lifespan', 0)} years")
        
        print("\n🧠 MENTAL TRAITS:")
        print(f"  IQ: {char['effective_stats'].get('iq', 0)}")
        print(f"  Perception: {char['effective_stats'].get('perception', 0)}")
        print(f"  Persuasion: {char['effective_stats'].get('persuasion', 0)}")
        
        print("\n🔮 MAGICAL TRAITS:")
        print(f"  Magic Affinity: {char['effective_stats'].get('magic_affinity', 0)}")
        print(f"  Elemental Damage Bonus: +{char['effective_stats'].get('elemental_damage_bonus', 0)}%")
        print(f"  Elemental Resistance: +{char['effective_stats'].get('elemental_resist_bonus', 0)}%")
        
        print("\n🧬 DNA HERITAGE:")
        for category, trait in char['dna_traits'].items():
            quality_indicator = ""
            if trait['quality'] == 'positive':
                quality_indicator = " ✨"
            elif trait['quality'] == 'negative':
                quality_indicator = " ⚠️"
            
            print(f"  {trait['category_name']}: {trait['name']}{quality_indicator}")
        
        print("\n🎯 STARTING POTENTIAL:")
        self.analyze_character_potential()
        
        print("═" * 60)
    
    def analyze_character_potential(self):
        """Analyze character's potential for different paths"""
        if not self.character:
            return
        
        stats = self.character['effective_stats']
        
        # Combat potential
        combat_score = (stats.get('STR', 0) + stats.get('DEX', 0) + stats.get('VIT', 0)) / 3
        print(f"  Combat Potential: {self.rate_potential(combat_score)}")
        
        # Magic potential
        magic_score = (stats.get('INT', 0) + stats.get('WILL', 0) + stats.get('magic_affinity', 0)) / 3
        print(f"  Magic Potential: {self.rate_potential(magic_score)}")
        
        # Social potential
        social_score = (stats.get('beauty', 0) + stats.get('persuasion', 0)) / 2
        print(f"  Social Potential: {self.rate_potential(social_score)}")
        
        # Survival potential
        survival_score = (stats.get('VIT', 0) + stats.get('perception', 0) + stats.get('immune_system', 0)) / 3
        print(f"  Survival Potential: {self.rate_potential(survival_score)}")
    
    def rate_potential(self, score):
        """Rate potential based on score"""
        if score >= 150:
            return "Legendary ⭐⭐⭐⭐⭐"
        elif score >= 120:
            return "Exceptional ⭐⭐⭐⭐"
        elif score >= 90:
            return "Good ⭐⭐⭐"
        elif score >= 60:
            return "Average ⭐⭐"
        else:
            return "Poor ⭐"
    
    def suggest_playstyle(self):
        """Suggest optimal playstyle based on character traits"""
        if not self.character:
            return
        
        stats = self.character['effective_stats']
        suggestions = []
        
        # Check for high magic affinity
        if stats.get('magic_affinity', 0) > 80:
            suggestions.append("🔮 Mage Path - Your magical potential is exceptional")
        
        # Check for high combat stats
        if stats.get('STR', 0) > 40 or stats.get('DEX', 0) > 40:
            suggestions.append("⚔️ Warrior Path - Your physical prowess is notable")
        
        # Check for high social stats
        if stats.get('persuasion', 0) > 120 or stats.get('beauty', 0) > 120:
            suggestions.append("🎭 Social Path - Your charisma opens many doors")
        
        # Check for high survival stats
        if stats.get('perception', 0) > 100 and stats.get('immune_system', 0) > 100:
            suggestions.append("🌲 Survivor Path - You're built to endure hardship")
        
        # Check for balanced stats
        if len(suggestions) == 0:
            suggestions.append("🎯 Balanced Path - Versatility is your strength")
        
        print("\n🎮 RECOMMENDED PLAYSTYLES:")
        for suggestion in suggestions:
            print(f"  {suggestion}")
    
    def run_full_demo(self):
        """Run the complete character generation demo"""
        try:
            # Generate character
            self.start_character_generation()
            
            # Display character sheet
            self.display_character_sheet()
            
            # Suggest playstyle
            self.suggest_playstyle()
            
            print("\n" + "🎉" * 30)
            print("CHARACTER READY FOR ADVENTURE!")
            print("🎉" * 30)
            print("\nYour journey in Aethermoor begins now...")
            print("Remember: You only get one life. Make it count.")
            
            return True
            
        except KeyboardInterrupt:
            print("\n\n❌ Character generation cancelled.")
            print("Your fate remains unwritten...")
            return False
        except Exception as e:
            print(f"\n❌ Error during character generation: {e}")
            return False


def main():
    """Main demo function"""
    print("=" * 60)
    print("ONE LIFE ISEKAI - CHARACTER GENERATION DEMO")
    print("=" * 60)
    
    game = GameWithCharacterGeneration()
    
    try:
        success = game.run_full_demo()
        return 0 if success else 1
    except Exception as e:
        print(f"Demo failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
