#!/usr/bin/env python3
"""
One Life Isekai - Main Entry Point
A dark fantasy single-life RPG where death is final

This is the main menu and game launcher.
The actual game logic will be implemented separately.
"""

import pygame
import sys
import os
from ui.menu_system import MainMenuSystem


def create_asset_directories():
    """Create necessary asset directories if they don't exist"""
    directories = [
        'assets',
        'assets/backgrounds',
        'assets/music',
        'assets/fonts',
        'config'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")


def main():
    """Main application entry point"""
    print("=" * 50)
    print("ONE LIFE ISEKAI")
    print("Where Death is Final")
    print("=" * 50)
    
    # Create asset directories
    create_asset_directories()
    
    try:
        # Initialize and run the main menu
        menu_system = MainMenuSystem()
        should_start_game = menu_system.run()
        
        if should_start_game:
            print("\n🎮 Starting game...")

            # Check if character was generated
            if hasattr(menu_system, 'character_data') and menu_system.character_data:
                character = menu_system.character_data
                print("✅ Character generation completed!")

                # Display character summary
                race = character['race']
                gender = character['gender']
                print(f"\n📜 Your Character:")
                print(f"   Race: {race['name']} ({race['rarity']}, {race['quality']})")
                print(f"   Gender: {gender['name']}")

                # Show key stats
                stats = character['effective_stats']
                print(f"   Key Stats: STR={stats.get('STR', 0)}, DEX={stats.get('DEX', 0)}, INT={stats.get('INT', 0)}")
                print(f"   Magic Affinity: {stats.get('magic_affinity', 0)}")
                print(f"   Beauty: {stats.get('beauty', 0)}")
                print(f"   Expected Lifespan: {stats.get('estimated_lifespan', 0)} years")

                print("\n🌟 Your journey in Aethermoor begins...")
                print("📝 TODO: Implement life simulation and world events")

                # Here you would start the actual game world
                # from game.world import GameWorld
                # game_world = GameWorld(character)
                # game_world.run()

            else:
                print("❌ Character generation was cancelled or failed.")
                print("   Returning to the void...")
            
        else:
            print("\n👋 Thanks for trying One Life Isekai!")
    
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure pygame is installed: pip install pygame")
        return 1
    
    finally:
        # Cleanup pygame
        pygame.quit()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
