#!/usr/bin/env python3
"""
One Life Isekai - Main Entry Point
A dark fantasy single-life RPG where death is final

This is the main menu and game launcher.
The actual game logic will be implemented separately.
"""

import pygame
import sys
import os
from ui.menu_system import MainMenuSystem


def create_asset_directories():
    """Create necessary asset directories if they don't exist"""
    directories = [
        'assets',
        'assets/backgrounds',
        'assets/music',
        'assets/fonts',
        'config'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"Created directory: {directory}")


def main():
    """Main application entry point"""
    print("=" * 50)
    print("ONE LIFE ISEKAI")
    print("Where Death is Final")
    print("=" * 50)
    
    # Create asset directories
    create_asset_directories()
    
    try:
        # Initialize and run the main menu
        menu_system = MainMenuSystem()
        should_start_game = menu_system.run()
        
        if should_start_game:
            print("\n🎮 Starting game...")
            print("⚠️  WARNING: This is where the actual game would begin!")
            print("📝 TODO: Implement character generation and game world")
            print("\nFor now, the main menu system is complete and functional.")
            
            # Here you would initialize and start the actual game
            # For example:
            # from game.character_generation import CharacterGenerator
            # from game.world import GameWorld
            # 
            # char_gen = CharacterGenerator()
            # character = char_gen.generate_character()
            # 
            # game_world = GameWorld(character)
            # game_world.run()
            
        else:
            print("\n👋 Thanks for trying One Life Isekai!")
    
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("Make sure pygame is installed: pip install pygame")
        return 1
    
    finally:
        # Cleanup pygame
        pygame.quit()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
