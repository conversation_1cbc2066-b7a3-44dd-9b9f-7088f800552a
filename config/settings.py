"""
Settings management for One Life Isekai
Handles loading, saving, and managing game configuration
"""

import json
import os
from typing import Dict, Any
from utils.constants import DEFAULT_SETTINGS


class SettingsManager:
    def __init__(self, settings_file: str = "settings.json"):
        self.settings_file = settings_file
        self.settings = DEFAULT_SETTINGS.copy()
        self.load_settings()
    
    def load_settings(self) -> None:
        """Load settings from JSON file, create with defaults if not exists"""
        try:
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r') as f:
                    loaded_settings = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    self._merge_settings(loaded_settings)
            else:
                # Create settings file with defaults
                self.save_settings()
        except (json.JSONDecodeError, IOError) as e:
            print(f"Error loading settings: {e}. Using defaults.")
            self.settings = DEFAULT_SETTINGS.copy()
    
    def _merge_settings(self, loaded_settings: Dict[str, Any]) -> None:
        """Merge loaded settings with defaults to ensure all keys exist"""
        for category, values in DEFAULT_SETTINGS.items():
            if category in loaded_settings:
                if isinstance(values, dict):
                    for key, default_value in values.items():
                        if key not in loaded_settings[category]:
                            loaded_settings[category][key] = default_value
                self.settings[category] = loaded_settings[category]
            else:
                self.settings[category] = values.copy()
    
    def save_settings(self) -> None:
        """Save current settings to JSON file"""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self.settings, f, indent=4)
        except IOError as e:
            print(f"Error saving settings: {e}")
    
    def get(self, category: str, key: str = None) -> Any:
        """Get a setting value"""
        if key is None:
            return self.settings.get(category, {})
        return self.settings.get(category, {}).get(key)
    
    def set(self, category: str, key: str, value: Any) -> None:
        """Set a setting value"""
        if category not in self.settings:
            self.settings[category] = {}
        self.settings[category][key] = value
    
    def get_master_volume(self) -> int:
        """Get master volume (0-100)"""
        return self.get('audio', 'master_volume')
    
    def get_music_volume(self) -> int:
        """Get music volume (0-100)"""
        return self.get('audio', 'music_volume')
    
    def get_sfx_volume(self) -> int:
        """Get SFX volume (0-100)"""
        return self.get('audio', 'sfx_volume')
    
    def get_fullscreen(self) -> bool:
        """Get fullscreen setting"""
        return self.get('display', 'fullscreen')
    
    def get_text_speed(self) -> str:
        """Get text speed setting"""
        return self.get('gameplay', 'text_speed')
    
    def set_volume(self, volume_type: str, value: int) -> None:
        """Set volume (master, music, or sfx)"""
        self.set('audio', f'{volume_type}_volume', max(0, min(100, value)))
    
    def set_fullscreen(self, fullscreen: bool) -> None:
        """Set fullscreen mode"""
        self.set('display', 'fullscreen', fullscreen)
    
    def set_text_speed(self, speed: str) -> None:
        """Set text speed (Slow, Normal, Fast)"""
        if speed in ['Slow', 'Normal', 'Fast']:
            self.set('gameplay', 'text_speed', speed)


# Global settings instance
settings = SettingsManager()
